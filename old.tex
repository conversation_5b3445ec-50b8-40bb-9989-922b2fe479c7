
intro:


A major obstacle to their widespread adoption, however, is their limited long-term stability, which degrades rapidly under environmental stressors such as moisture, heat, and ultraviolet radiation \cite{perini2021pressing}. Recent outdoor and accelerated laboratory tests \cite{ali2023outdoor} have shown that despite newly achieved high efficiencies, to properly evaluate the stability of PSCs, they must be evaluted under real world conditions. PSCs are highly susceptible to degradation from illumination, electrical bias, moisture, heat, etc \cite{zhou2022recent} \cite{zhang2022degradation} \cite{liu2020uv} \cite{dipta2021stability}. Despite the proven importance of these assessments, conventional stability testing equipment is often prohibitively expensive, frequently costing tens of thousands of dollars per unit \cite{keesey2023opensource}, or can only measure a few cells at a time. Such high costs restrict large scale parallel testing and limit the capacity of academic and small-scale research laboratories to conduct comprehensive stability studies. The development of cost-effective, open-source testing platforms is therefore crucial for access to high-throughput stability assessments. Recent advancements in low-cost environmental chambers and automated testing systems \cite{keesey2023opensource} have paved the way for more accessible approaches that maintain rigorous performance evaluation.

In response to these challenges, we introduce an open-source, Arduino-based Stability Measurement System (SMS) for long-term stability testing of perovskite solar cells. With a production cost of only \$50 per unit, our platform is engineered to simultaneously monitor multiple substrates and perform parallel maximum power point tracking (MPPT) measurements. By integrating readily available hardware components onto a custom printed circuit board (PCB), our system significantly lowers the financial barrier to comprehensive stability testing while offering a scalable and customizable solution for a wide range of research environments. We also created an intuitive, open-source data acquisition and control graphical user interface (GUI) for interfacing with the hardware.

All design plans and materials described in this paper are freely available online, enabling easy reproduction. In this paper, we compare our open-source system with a conventional commercially available stability measurement system (CAS). We evaluate both current-voltage (JV) and MPPT measurements, demonstrating that our system maintains an accuracy of $\pm$1 PCE\% in power conversion efficiency (PCE) during long-term MPPT testing. Ultimately, our approach aims to accelerate the iterative development of perovskite materials by providing a reliable, accessible, and cost-effective tool for stability assessment.

system design:



A single sms unit is capable of measuring 8 cells in parallel, connecting to the cathode and anode of each cell separately. It has 16 output pins, which exist in the form of a ribbon cable connector. The SMS supports parallel JV and MPPT measurements on all 8 cells individually. Each SMS unit connects to a Windows PC via a USB cable, providing both power and data connectivity. It also connects to a custom designed sample holder using a ribbon cable, shown in Figure~\ref{fig:system_design_4}. Depending on the PC’s hardware specifications and the version of Windows, a single PC can manage more than 32 SMS units (256 cells) in parallel. For configurations with more than four SMS units, a powered USB expansion hub may be necessary. Each SMS operates independently while continuously communicating with the central PC (Figure~\ref{fig:system_design_1}) for data logging, processing, and visualization.

The SMS can apply a voltage bias ranging from 0 to 3.3 V with a resolution of 0.8 mV. It is capable of reading voltages from -0.3 to 26 V with a resolution of 4 mV and measuring currents from 0 to 32 mA with a resolution of 8 µA. It has been tested to run for over 500 hours without error, but should theoretically run until the PC runs out of storage space.

The SMS GUI provides a user-friendly interface that is crucial to the system's operation. Through the GUI, users can easily configure measurement parameters, such as scan range, voltage steps, and sampling rates, tailoring the system for a variety of substrates and test conditions. Users can queue up multiple tests that run sequentially, simplifying long term testing. These queues are also automatically saved for easy access later on. Real-time visualization of both JV and MPPT data is available, allowing for quick identification of performance trends and potential issues. Additionally, the GUI automatically logs all data to CSV files periodically. Users can easily access data for further post-processing or in depth analysis. A detailed user guide can be found in the linked github.