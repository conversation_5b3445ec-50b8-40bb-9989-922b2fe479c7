% \documentclass[onecolumn,10pt]{IEEEtran}
\documentclass[twocolumn,10pt]{article}

\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{hyperref}
\usepackage{cite}
\usepackage{subcaption}
\usepackage{authblk}
\usepackage{indentfirst}
\usepackage[letterpaper, margin=1in]{geometry}
\usepackage{booktabs}

\title{A Cost-Effective Arduino-Based System for Measuring Solar Panel Stability}
\author{<PERSON>}
\author{<PERSON>}
\author{<PERSON><PERSON><PERSON>}
\author{<PERSON>}
\author{<PERSON><PERSON>}
\author{<PERSON><PERSON><PERSON>}
\usepackage{lineno}

% allow up to 3 double‐column floats at the top of a page
\setcounter{dbltopnumber}{3}

% allow them to occupy up to 80% of the page height
\renewcommand{\dbltopfraction}{.8}

% make sure LaTeX can still put text on that page
\renewcommand{\textfraction}{.1}
\renewcommand{\floatpagefraction}{.75}

% TODO: talk about difference in silver paste
% moving average the long trace
%  - in the introduction add a couple of lines on what has been done on equipment design in research.
% talk about difference in litos holder and my design

\affil{School of Materials Science and Engineering, School of Chemistry and Biochemistry, Center for Organic Photonics and Electronics, Georgia Institute of Technology, North Ave NW, Atlanta, Georgia 30332, USA}

\date{}

\begin{document}
% \linenumbers

\maketitle


\newpage

\begin{abstract}
Perovskite solar cells have achieved high power conversion efficiencies, but their long-term stability requires improvement for widespread commercial adoption. Efficient stability testing is critical for advancing this technology. However, the high cost of commercial stability analyzers presents a significant barrier for many research laboratories.

We have developed an open-source Stability Measurement System (SMS) based on the Arduino platform. This system provides essential measurement capabilities at approximately 1\% of the cost of commercial alternatives. Each \$100 SMS module enables simultaneous current-voltage (JV) characterization and maximum power point tracking (MPPT) on eight separate solar cells. The system's modular design allows for expansion to monitor over 250 cells concurrently. The SMS consists of custom printed circuit boards, 3D-printed sample holders, and Python-based control software. Minimal skills are required to assemble.

Validation against a commercial system demonstrated acceptable measurement accuracy, with relative standard deviations of 1.26\% for fill factor and 0.34\% for power conversion efficiency. A 200-hour stability test confirmed the system's reliability for long-term studies. While variability in contact resistance limits the system's precision for absolute benchmarking, the SMS is effective as a screening platform for comparative stability studies. By providing greater access to high-throughput aging studies, this open-source platform can facilitate broader exploration of perovskite compositions and accelerate research toward commercial viability.
\end{abstract}

\input{sections/introduction.tex}

\input{sections/system_description.tex}

\input{sections/experimental.tex}

\input{sections/results_and_discussion.tex}

\section{Conclusion}
We have developed and validated an open-source, Arduino-based Stability Measurement System (SMS) for high-throughput solar cell stability analysis. At approximately 1\% of the cost of commercial equipment, the system significantly lowers the financial barrier for comprehensive stability testing. The modular system performs parallel current-voltage characterization and maximum power point tracking on numerous cells simultaneously, enabling large-scale screening experiments. Validation against a commercial analyzer confirmed the SMS provides sufficient accuracy for comparative stability studies, with relative standard deviations of 1.26\% for fill factor and 0.34\% for power conversion efficiency. While not intended to replace high-precision commercial systems, the SMS is an effective tool for preliminary screening of materials and device architectures. By making high-throughput aging studies more accessible, this open-source platform can facilitate the accelerated development and eventual commercialization of stable perovskite solar cells.


\newpage

\bibliographystyle{IEEEtran}
\bibliography{references}

\end{document}
