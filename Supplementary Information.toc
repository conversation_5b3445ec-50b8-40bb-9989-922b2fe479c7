\contentsline {section}{\numberline {I}Detailed Component Specifications}{2}{section.1}%
\contentsline {subsection}{\numberline {\mbox {I-A}}PCB schematics and layout diagrams}{2}{subsection.1.1}%
\contentsline {subsection}{\numberline {\mbox {I-B}}Arduino microcontroller specifications}{3}{subsection.1.2}%
\contentsline {subsection}{\numberline {\mbox {I-C}}DAC and ADC component datasheets and configurations}{3}{subsection.1.3}%
\contentsline {subsection}{\numberline {\mbox {I-D}}3D-Printed Components and Sample Holder}{3}{subsection.1.4}%
\contentsline {subsection}{\numberline {\mbox {I-E}}Power and Inter-module Communication}{3}{subsection.1.5}%
\contentsline {subsection}{\numberline {\mbox {I-F}}Illumination System Specifications}{3}{subsection.1.6}%
\contentsline {section}{\numberline {II}Assembly Instructions and Guidelines}{4}{section.2}%
\contentsline {subsection}{\numberline {\mbox {II-A}}Step-by-step PCB assembly guide}{4}{subsection.2.1}%
\contentsline {subsection}{\numberline {\mbox {II-B}}Instructions for soldering and wiring}{4}{subsection.2.2}%
\contentsline {subsection}{\numberline {\mbox {II-C}}3D printing guidelines, files, and recommended print settings}{4}{subsection.2.3}%
\contentsline {subsection}{\numberline {\mbox {II-D}}Enclosure assembly and sample holder setup}{4}{subsection.2.4}%
\contentsline {subsection}{\numberline {\mbox {II-E}}Troubleshooting common assembly issues}{4}{subsection.2.5}%
\contentsline {section}{\numberline {III}Software Implementation and Usage}{4}{section.3}%
\contentsline {subsection}{\numberline {\mbox {III-A}}Python control software setup instructions}{4}{subsection.3.1}%
\contentsline {subsection}{\numberline {\mbox {III-B}}Installation and dependency management (e.g., PyQt, serial communication)}{4}{subsection.3.2}%
\contentsline {subsection}{\numberline {\mbox {III-C}}GUI user manual (detailed screenshots and explanations)}{4}{subsection.3.3}%
\contentsline {subsection}{\numberline {\mbox {III-D}}Example scripts for automated testing procedures}{4}{subsection.3.4}%
\contentsline {section}{\numberline {IV}Complete Experimental Data}{4}{section.4}%
\contentsline {subsection}{\numberline {\mbox {IV-A}}Raw JV curves from diode baseline validation}{4}{subsection.4.1}%
\contentsline {subsection}{\numberline {\mbox {IV-B}}Complete dataset for separate holder protocol experiment}{5}{subsection.4.2}%
\contentsline {subsection}{\numberline {\mbox {IV-C}}Complete dataset for same holder protocol experiment}{5}{subsection.4.3}%
\contentsline {subsection}{\numberline {\mbox {IV-D}}Raw data and extended analysis for repeatability measurements}{5}{subsection.4.4}%
\contentsline {subsection}{\numberline {\mbox {IV-E}}Extended dataset and degradation curves from 200‑hour stability test}{5}{subsection.4.5}%
\contentsline {section}{\numberline {V}Future Development and Community Contributions}{5}{section.5}%
\contentsline {subsection}{\numberline {\mbox {V-A}}Roadmap for integration with environmental chambers and advanced sensing components}{5}{subsection.5.1}%
\contentsline {subsection}{\numberline {\mbox {V-B}}Guidelines for community-driven enhancements and custom modifications}{5}{subsection.5.2}%
\contentsline {subsection}{\numberline {\mbox {V-C}}Instructions for contributing improvements and reporting issues on the open-source repository}{5}{subsection.5.3}%
\contentsline {section}{References}{5}{section*.4}%
\contentsline {section}{\numberline {VI}Associated Links}{6}{section.6}%
