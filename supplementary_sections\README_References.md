# Supplementary References Guide

## Overview
This document explains how to use the supplementary reference system for your Arduino-based solar panel stability measurement system documentation.

## Files Created
1. **`supplementary_references.bib`** - Main bibliography file containing references specific to supplementary sections
2. **Updated `Supplementary Information.tex`** - Main supplementary document with bibliography integration
3. **Updated `Associated_Links.tex`** - Links section (moved to end after bibliography)

## Reference Categories

### Hardware Components
- `arduino_nano` - Arduino Nano documentation
- `mcp4725_datasheet` - MCP4725 DAC datasheet
- `ina219_datasheet` - INA219 ADC datasheet
- `atmega328p_datasheet` - ATmega328P microcontroller datasheet

### Software and Libraries
- `pyside6` - PySide6 Qt for Python
- `adafruit_libraries` - Adafruit Arduino libraries
- `python_serial` - PySerial documentation
- `matplotlib`, `numpy`, `pandas` - Python data analysis libraries

### Manufacturing and Design
- `easyeda` - EasyEDA PCB design platform
- `jlcpcb_supplementary` - JLCPCB manufacturing services
- `bambu_lab_general` - Bambu Lab 3D printers
- `open_source_hardware` - Open source hardware definition

### Standards and Protocols
- `i2c_specification` - I2C bus specification
- `ieee_photovoltaic` - IEEE photovoltaic standards

### Materials
- `pla_material`, `abs_material` - 3D printing materials
- `led_grow_lights` - LED illumination systems

## How to Use

### Adding Citations
In your `.tex` files, use the `\cite{}` command:
```latex
The Arduino Nano \cite{arduino_nano} controls the measurement system.
```

### Multiple Citations
```latex
Components are sourced from various suppliers \cite{jlcpcb_supplementary,arduino_nano}.
```

### Adding New References
1. Open `supplementary_references.bib`
2. Add new entries following the existing format:
```bibtex
@misc{new_reference,
  author       = {Author Name},
  title        = {Title},
  howpublished = {\url{https://example.com}},
  year         = {2025},
  note         = {Accessed: 2025-07-25}
}
```

### Compilation Order
To properly compile the document with references:
1. Run LaTeX on `Supplementary Information.tex`
2. Run BibTeX on the generated `.aux` file
3. Run LaTeX again (twice) to resolve all references

## Example Usage
The `Detailed_Components.tex` file has been updated with example citations showing how to reference:
- PCB design tools (`\cite{easyeda}`)
- Manufacturing services (`\cite{jlcpcb_supplementary}`)
- Component datasheets (`\cite{mcp4725_datasheet}`)
- Communication protocols (`\cite{i2c_specification}`)

## Customization
- Update URLs in the `.bib` file to match your actual repository
- Add specific version numbers for software components
- Include DOIs where available for academic references
- Adjust citation styles by modifying the `\bibliographystyle{}` command

## References Added to Supplementary Sections

### Software Implementation (`Software_Implementation.tex`)
- `pyside6` - PySide6 GUI framework
- `matplotlib` - Data visualization
- `python_serial` - Serial communication
- `numpy` - Numerical computing
- `pandas` - Data analysis

### Firmware (`Firmware.tex`)
- `arduino_nano` - Arduino Nano documentation
- `atmega328p_datasheet` - Microcontroller datasheet
- `adafruit_libraries` - Arduino libraries

### Assembly Instructions (`Assembly_Instructions.tex`)
- `stability_setup_supplementary` - GitHub repository
- `jlcpcb_supplementary` - PCB manufacturing
- `smd_soldering` - Soldering guidelines
- `mcp4725_datasheet`, `ina219_datasheet` - Component datasheets
- `i2c_specification` - Communication protocol
- `abs_material`, `pla_material` - 3D printing materials
- `bambu_lab_general` - 3D printer reference
- `open_source_hardware` - Open source principles

### Detailed Components (`Detailed_Components.tex`)
- `easyeda` - PCB design software
- `component_pricing` - Cost analysis
- `Samir2020LED` - LED solar simulator reference

### Future Development (`Future_Development.tex`)
- `khenkin2020consensus` - Stability testing standards
- `led_grow_lights` - Illumination systems

### Extended Accuracy (`Extended_Accuracy.tex`)
- `esram2007comparison` - MPPT algorithms

### Additional Experiment Data (`Additional_Experiment_Data.tex`)
- All Python library references for data analysis

### Electrical (`Electrical.tex`)
- Complete electrical documentation references

### Associated Links (`Associated_Links.tex`)
- Repository and file format references

## Notes
- The bibliography appears before the "Associated Links" section
- All references are formatted using IEEE style
- URLs are automatically formatted as clickable links in the PDF
- The system is designed to be modular - you can easily add or remove reference categories
- Citations are now integrated throughout all supplementary sections
- References cover hardware, software, standards, and manufacturing aspects
