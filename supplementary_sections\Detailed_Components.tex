\section{Detailed Component Specifications}


% GOOD REFERENCE FOR COMPELX DESIGN
% https://pdf.sciencedirectassets.com/277910/1-s2.0-S1876610215X00142/1-s2.0-S1876610215014095/main.pdf?X-Amz-Security-Token=IQoJb3JpZ2luX2VjEOT%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJIMEYCIQDB98U9JS4xkMFwHSP8xpl6S3WwgDsX%2BIEsW8zfDhAgSAIhAM7N67xXxyjjcjc8uP0EB89tedaI1mgHs0j4aLK%2BhcVMKrEFCH0QBRoMMDU5MDAzNTQ2ODY1IgzC%2B9Pr8Eyt0PK9oCAqjgV9gNHlyzkbxbrudQP%2B%2FDhbkqWUEwAtSLtwWQbN3xNyc4jynGkazuuxQJfyt%2BanVTZ59bJoeLfmckleNuX2aEu13c%2FQo2bmEiX1%2BrJKd4IOkjpZiiCccJgv7aB3WaPGVHGCcxa5xkxq64mvtmXNxwyqNbjHPsvUYoHbry6lU%2FptZRmRJ7yOQBshkJTnfwRzE0G7qZpx2ovXNIm010sD2siDGfecMDj%2BCSyuOA%2B45KtiF8N%2Fj9pVQUG7zjqCeqSCHjmZ5z92C5%2FXlXDVgu7iJjXUjrbpdgthOmLl9J%2FS0OBODJFRpnSBZI7br1bxOP1lQND%2FtwuLBMpHPrmKFh9EsK63EzhBF1gKu0Otj%2BqXGoIU6Tblo88nEMadfn4OPUShdaxO%2B%2BlY5%2FI7mk%2FHIy7XhsPdpWQAORjOWvL3TB7r3uKJ%2FSychEbukmjtqeUi%2BbUHSB1b2IhW8%2Bb5GuK3ASeuzXOLug%2BcY0rjSBZyDYTMbQd5JvUcGl4cGXUBmC%2BJlRVOgTDiNKpmt3dRiS%2B1jFU1RNBhK4o0Ra6meEXC8YzkddWVhVi08NrVAFXsMpohBCxV6lY7Mxr6takg8jG63kO0vSEDzrNqO9WMfnMcpOmS5vInTbInDGlNmapCajO1fMFFy%2ByeR%2FWxtDAw%2FqWZQnNpWsqtlTmyg0ncETXIkXVT8%2BgLndDRXH%2BRz%2FUr4xTTECwFTkRUMLvlG4WgoeByxhcC3vdzXdZzFNSK64Mp1pT9155o6BppMdvIM6touQk4NGnASF8JHviUa7fkWRTmxDezlIjKVB3UP8TXP8hWdzd%2BQKbAjBaIm0zK0qH%2FCXmH0UAWi8lz1poi3bgpEK7xEo7lvwHeZevuyKEp2Jz3ckVbbPYw37C%2FwAY6sAGzUFOGDtPtfUIfbjK4iZ5q8pPxCPyNK2ehaT73WFQRqtuuGGd9z%2B%2FfBZ9yv74pRHwAB%2FXmRlDnIlFz87yCviOPzSy%2FnaJERb4WrCnulvmTRzzThxa%2B2tTBkRysrltWbqCqUJRqMuPIgxTVY%2BiS4DhbdYUXhwM3nSOOVY0ps4Fs83FJ36KZ5kDlaja6mbhrQFz%2FGiGvZ28EzbAld4Nh8PpNR94zy0I0I27mJ8yckO%2F0Og%3D%3D&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250428T200925Z&X-Amz-SignedHeaders=host&X-Amz-Expires=300&X-Amz-Credential=ASIAQ3PHCVTY3BC655JE%2F20250428%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=fb6430f912cff88f32330ad42c24c5b7ad9e3098d87a807abc807f4a8a0851eb&hash=da4b77759521b9e5861623bde5a69eeb51e7fce63f583efeb4c9ea4cf610a4d2&host=68042c943591013ac2b2430a89b270f6af2c76d8dfd086a07176afe7c76c2c61&pii=S1876610215014095&tid=spdf-daa177bb-f650-4e74-8ac7-30856f95b256&sid=6794535b15ea924c1019dfb03678cb873042gxrqa&type=client&tsoh=d3d3LnNjaWVuY2VkaXJlY3QuY29t&rh=d3d3LnNjaWVuY2VkaXJlY3QuY29t&ua=0f155957565a5450540653&rr=9379327a4e539c5e&cc=us

\subsection{PCB schematics and layout diagrams}

\begin{figure*}[ht]
    \centering
    % Row 1
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{supplementary_sections/images/SCHEMATIC.png}
        \caption{Detailed schematic for a single measurement channel.}
        \label{fig:schematic}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/pcb.png}
        \caption{Rendered 3D view of the main PCB layout.}
        \label{fig:pcb_layout}
    \end{subfigure}

    \vspace{1em} % space between rows

    % Row 2
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{supplementary_sections/images/circuit.png}
        \caption{Simplified circuit diagram for a single cell assembly.}
        \label{fig:simplecircuit}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/PROBE.png}
        \caption{Assembled measurement probe with pogo pins.}
        \label{fig:probe_config}
    \end{subfigure}

    \caption{Electrical diagrams and component layouts of the stability measurement system. (a) Detailed schematic of a single measurement channel, showing the DAC, ADC, and shunt resistor configuration. (b) 3D rendering of the assembled main PCB. (c) Simplified circuit diagram illustrating the basic measurement principle. (d) Photograph of the assembled probe head.}
    \label{fig:circuit_and_layout}
\end{figure*}

The main board of the Stability Measurement System (SMS) was designed in EasyEDA, with fabrication and assembly handled by JLCPCB \cite{jlcpcb}. All design files, including schematics and PCB layouts, are open-source to facilitate replication and modification by other researchers. The board is engineered to test up to eight solar cell pixels in parallel, with each pixel monitored by an independent sub-circuit.

The core of each sub-circuit, detailed in the schematics in Figures \ref{fig:schematic} and \ref{fig:simplecircuit}, consists of two primary integrated circuits (ICs) that operate over an I$^2$C bus:
\begin{itemize}
    \item \textbf{MCP4725 DAC:} A digital-to-analog converter used to apply a precise load voltage to the solar cell pixel. Each of the eight DACs is assigned a unique I$^2$C address from 0x60 to 0x67. The DAC can supply a voltage from 0 to 3.3V with a 0.8 mV resolution.
    \item \textbf{INA219 ADC:} A high-side current and voltage sensor that monitors the cell's output. Each of the eight ADCs is assigned a unique I$^2$C address from 0x40 to 0x47. The sensor measures the voltage drop across a 10 $\Omega$ shunt resistor to determine the current. This configuration provides a current measurement range of 0 to 32 mA with a resolution of 8 µA and a voltage measurement range of 0 to 27 V with a resolution of 4 mV.
\end{itemize}

In the measurement circuit (Figure \ref{fig:schematic}), the MCP4725 DAC (P0) applies a variable load voltage, ranging from 0 to 3.3 V with a 12-bit resolution (approximately 0.8 mV per step), to the connected solar cell pixel. The INA219 ADC (I0) then measures the resulting voltage and current. Current is measured by monitoring the voltage drop across a high-precision $10\Omega$ shunt resistor (R3). This configuration allows for current measurements in the range of 0-32 mA with a resolution of 8 µA. To ensure stable power delivery and reduce signal noise, decoupling capacitors (C1, C2, C3) are placed on the voltage input line.

The entire measurement process for all eight channels is orchestrated by an Arduino Nano \cite{arduino_nano} (AN), which communicates with the DAC and ADC components via the I$^2$C protocol. The selection of the MCP4725 and INA219 components was based on their low cost, high availability, and robust software support, with well-documented C++ libraries provided by Adafruit \cite{adafruit_libraries}. The measurement ranges afforded by these components are well-suited for typical perovskite solar cell characterization. For applications requiring higher precision or different voltage/current ranges, these components can be substituted with pin-compatible alternatives. Figure \ref{fig:pcb_layout} shows a 3D rendering of the final PCB layout, while Figure \ref{fig:probe_config} shows the assembled holder PCB used for contacting the solar cell pixels.

\subsection{Arduino microcontroller specifications}

The system is controlled by an Arduino Nano \cite{arduino_nano}, selected for its compact form factor, widespread availability, and extensive community support. The Nano is based on the ATmega328P microcontroller \cite{atmega328p_datasheet}, which provides sufficient computational power for managing the measurement tasks.

A critical parameter for this application is the microcontroller's flash memory, which dictates the maximum program size. The firmware for the SMS, which includes libraries for the DAC and ADC components as well as the control logic for running the stability tests, occupies approximately 14 KB. This leaves ample space within the Arduino Nano's 32 KB of flash memory for future expansion or the addition of more complex measurement routines.

While the system was designed around the Arduino Nano, it is compatible with other Arduino boards. The primary requirements for a substitute board are sufficient flash memory to accommodate the firmware and support for the I$^2$C communication protocol \cite{i2c_specification}, which requires at least one set of SDA and SCL pins. Boards such as the Arduino Uno or Arduino Mega meet these criteria and could be used with minimal modification to the firmware's pin definitions.


\subsection{DAC and ADC component datasheets and configurations}


\begin{table*}[ht]
  \centering
  \caption{Bill of materials and cost breakdown for five Stability Measurement System (SMS) modules. PCB prices include fabrication, component sourcing, and assembly. Users may source parts and perform assembly manually; however, this requires proficiency in surface-mount device (SMD) soldering.}
  \label{tab:bom_fullwidth}
  {\large
  \renewcommand{\arraystretch}{1.2}
  \begin{tabular*}{\textwidth}{@{\extracolsep{\fill}} l c r r}
  \hline
  \textbf{Component} & \textbf{Qty} & \textbf{Unit Cost (\$)} & \textbf{Total Cost (\$)} \\
  \hline
  Arduino Nano (or compatible clone) & 5 & 7.00 & 35.00 \\
  Main PCB assembly & 5 & 40.50 & 202.50 \\
  Holder PCB & 5 & 11.40 & 57.00 \\
  3D-printed enclosure (Black PLA) & 5 & 2.00 & 10.00 \\
  LED grow light & 1 & 60.00 & 60.00 \\
  Cables and connectors & 10 & 2.00 & 20.00 \\
  \hline
  \textbf{Grand Total} & & & \textbf{384.50} \\
  \hline
  \end{tabular*}
  }
\end{table*}

\subsection{3D-Printed Components and Sample Holder}
It is recommended that the sample holder be printed out of ABS \cite{abs_material}, ASA, or some similarly heat resistant material, as printing out of regular PLA \cite{pla_material} will result in warping after hundreds of hours of use under continuous illumination.

We used Bambu Lab printers \cite{bambu_lab_general} for our prints and Bambu-sliced files are available in the associated repository \cite{stability_setup_supplementary}. The 3D printing files are designed following open-source hardware principles \cite{open_source_hardware} to ensure reproducibility across different printer types and settings.


\subsection{Power and Inter-module Communication}
The system operates on 5V DC power supplied via USB connection to the Arduino Nano \cite{arduino_nano}. The I$^2$C communication protocol \cite{i2c_specification} enables daisy-chaining multiple measurement modules for expanded testing capacity.

\subsection{Illumination System Specifications}
The stability testing setup utilizes LED grow lights \cite{led_grow_lights} to provide consistent illumination for solar cell characterization. LED-based illumination systems offer advantages over traditional halogen sources, including improved spectral stability, reduced heat generation, and longer operational lifetime \cite{Samir2020LED}. The illumination intensity and spectral characteristics should be calibrated against standard solar simulators to ensure accurate performance measurements.
