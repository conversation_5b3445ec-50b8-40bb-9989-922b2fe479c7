# LaTeX Workshop Configuration Guide

## Overview
Your VS Code workspace is now configured with LaTeX Workshop to build both your main document and supplementary information with proper bibliography support.

## Available Build Recipes

When you use **Ctrl+Alt+B** (or Cmd+Option+B on Mac) or the "LaTeX Workshop: Build LaTeX project" command, you'll see these options:

### 📄 Build Current Document (pdflatex + bibtex) [DEFAULT]
- Builds whichever document you currently have open
- Runs: pdflatex → bibtex → pdflatex → pdflatex
- **Use this when**: You're working on either document and want to build just that one

### 📋 Build Main Document Only
- Always builds: `A Cost-Effective System for Measuring Perovskite Solar Cell Stability.tex`
- Runs: pdflatex → bibtex → pdflatex → pdflatex
- **Use this when**: You want to build only the main paper, regardless of which file is open

### 📑 Build Supplementary Information Only
- Always builds: `Supplementary Information.tex`
- Runs: pdflatex → bibtex → pdflatex → pdflatex
- **Use this when**: You want to build only the supplementary document

### 📚 Build Both Documents (Main + Supplementary)
- Builds both documents in sequence
- Runs the full build process for main document, then supplementary
- **Use this when**: You've made changes that affect both documents or want to ensure both are up-to-date

### ⚡ Quick Build (pdflatex only)
- Single pdflatex pass on current document
- **Use this when**: You're making small changes and don't need bibliography updates

## How to Use

### Method 1: Command Palette
1. Press **Ctrl+Shift+P** (Cmd+Shift+P on Mac)
2. Type "LaTeX Workshop: Build"
3. Select "LaTeX Workshop: Build LaTeX project"
4. Choose your desired recipe from the list

### Method 2: Keyboard Shortcut
1. Press **Ctrl+Alt+B** (Cmd+Option+B on Mac)
2. Choose your desired recipe from the dropdown

### Method 3: VS Code Tasks (Alternative)
1. Press **Ctrl+Shift+P** (Cmd+Shift+P on Mac)
2. Type "Tasks: Run Task"
3. Choose from:
   - "Build Main Document with BibTeX"
   - "Build Supplementary Information with BibTeX"
   - "Build Both Documents"

## File Structure
```
your-project/
├── A Cost-Effective System for Measuring Perovskite Solar Cell Stability.tex
├── Supplementary Information.tex
├── references.bib (shared by both documents)
├── sections/
│   ├── introduction.tex
│   ├── experimental.tex
│   └── ...
├── supplementary_sections/
│   ├── Software_Implementation.tex
│   ├── Detailed_Components.tex
│   └── ...
└── .vscode/
    ├── settings.json
    └── tasks.json
```

## Key Features

### ✅ Shared Bibliography
- Both documents use the same `references.bib` file
- All supplementary references are included in the main bibliography
- No need to maintain separate reference files

### ✅ Automatic Cleanup
- Auxiliary files (`.aux`, `.log`, etc.) are automatically cleaned after successful builds
- Keeps your workspace tidy

### ✅ Error Handling
- Build errors are displayed in the VS Code Problems panel
- Detailed logs available in the Output panel

### ✅ PDF Preview
- Built PDFs open automatically in VS Code tabs
- SyncTeX support for clicking between source and PDF

## Troubleshooting

### Build Fails
1. Check the **Output** panel (View → Output → LaTeX Workshop)
2. Look for specific error messages
3. Common issues:
   - Missing citations: Add them to `references.bib`
   - Special characters: Escape them properly in LaTeX
   - File paths: Ensure all included files exist

### Bibliography Issues
1. Make sure all `\cite{}` commands reference entries in `references.bib`
2. Check BibTeX syntax in the references file
3. The build process automatically runs BibTeX, so citations should resolve

### Performance
- Use "Quick Build" for minor changes
- Use full builds when adding new citations or major changes
- The "Build Both Documents" recipe takes longer but ensures everything is synchronized

## Customization

### Adding New Recipes
Edit `.vscode/settings.json` and add new entries to the `latex-workshop.latex.recipes` array.

### Changing Default Recipe
Modify the `latex-workshop.latex.recipe.default` setting in `.vscode/settings.json`.

### Adding New Tools
Add entries to the `latex-workshop.latex.tools` array for custom commands.

## Tips

1. **Always use the full build** (with BibTeX) when you add new citations
2. **Use "Build Both Documents"** before submitting to ensure consistency
3. **Check both PDFs** after major changes to verify everything compiled correctly
4. **Keep references.bib organized** - it's shared between both documents
5. **Use the default recipe** for most day-to-day editing

## Status
✅ Configuration is active and ready to use
✅ Both documents build successfully
✅ Bibliography integration working
✅ All recipes tested and functional
