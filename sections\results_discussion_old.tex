\section{Results and Discussion}

\begin{figure*}[ht]
    \centering
    %--- First row of subfigures
    \begin{subfigure}[b]{0.45\textwidth}
        \centering
        \includegraphics[width=\textwidth]{sections/images/Comparison_FF_BoxPlot.png}
        \caption{}
        \label{fig:subfig_measurement1}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \centering
        \includegraphics[width=\textwidth]{sections/images/Comparison_PCE_BoxPlot.png}
        \caption{}
        \label{fig:subfig_measurement2}
    \end{subfigure}

    %--- Second row of subfigures
    \begin{subfigure}[b]{0.45\textwidth}
        \centering
        \includegraphics[width=\textwidth]{sections/images/VOLTAGE.png}
        \caption{}
        \label{fig:subfig_measurement3}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \centering
        \includegraphics[width=\textwidth]{sections/images/CURRENT.png}
        \caption{}
        \label{fig:subfig_measurement4}
    \end{subfigure}

    \caption{%
      Best results of comparison tests.
      (a)~JV measurement of SMS plotted against CAS. The absolute difference in V\textsubscript{OC} was 16 mV, the absolute difference in V\textsubscript{MPP} was 29mV.
      (b)~MPPT measurement of SMS plotted against commercial system. The absolute difference in the PCE\% after stabilization was 0.20\%.
      (c)~Voltage values from MPPT measurement of SMS plotted against commercial system.
      (d)~Current density values from MPPT measurement of SMS plotted against commercial system.
    }
    \label{fig:measurementResults}
\end{figure*}

\begin{figure*}[ht]
    \centering
    \includegraphics[width=1\textwidth]{sections/images/LONG.png}
    \caption{200 hour stability measurement.}
    \label{fig:stability}
\end{figure*}

\subsection{Experimental Setup}

To validate the base accuracy of the SMS, we conducted a JV scan on a sample with a silicon diode in place of a solar cell. This result was compared to the JV scan of the same sample on a CAS. The diode was illuminated under a WaveLabs Sinus 70 \cite{wavelabs_sinus70} solar simulator calibrated to deliver 1 SUN (100 mW/cm²) irradiance with AM 1.5G spectrum.

To validate performance on PSCs, the SMS was benchmarked against a CAS. For consistency, the same commercial light source was used in both trials. The light source used was a WaveLabs Sinus 70 \cite{wavelabs_sinus70}, emitting am AM 1.5G solar spectrum. JV scans and MPPT tracking between the two systems were directly comapred. 4 perovskite substrates, each consisting of 8 solar cells (A total of 32 cells), were utilized for all tests. These substrates were measured in parallel by both the SMS and CAS. They had an active area of $\sim$0.128$cm^2$. No masks were used as the active area could change between setups, since the cells needed to be moved between holders and the masks would be difficult to place in exactly the same position on the substrates. The waiting time between the measurements was less than 5 minutes. The metrics compared were fill factor (FF), open circuit voltage (V\textsubscript{OC}), and short circuit current (J\textsubscript{SC}).

A separate test was conducted to validate performance of the SMS, without the variable of the custom designed substrate holder. An adapter was made and connected the SMS main board to the CAS sample holder. This experiment was conducted to remove any error introduced by moving the samples between holders and the associated exposure to oxygen. The setup was the same as the previously mentioned experiment, except only 4 solar cells were used. The same metrics were compared as well.

We also wanted to validate the repeatability of the SMS. To do this, we ran back to back 4 JV and MPPT measurements on a single PSC substrate. From this, we calculated the relative standard deviation for FF, V\textsubscript{OC}, and J\textsubscript{SC} for the JV curves, and the PCE\% after stabilization for the MPPT measurements.

To assess the system’s long-term stability testing capability, we conducted a 200 hour degradation experiment using 32 solar cells measured in parallel. A cost-effective LED grow light served as the illumination source and was calibrated by adjusting its height to deliver 1 SUN equivalent irradiance, as measured by a calibrated reference solar panel. Although the LED spectrum does not fully replicate AM 1.5G sunlight, it provided adequate conditions for controlled degradation and repeatable stability assessment under laboratory conditions. Silver paste was used on the contacts of the cells to ensure consistent contact over the period of the test.

\subsection{Diode JV Scan Results}

The absolute differences between the two setups were 0.035 for FF, 0.128 mA for J\textsubscript{SC}, and 0.008 V for V\textsubscript{OC}. These differences represent relative errors of approximately 6.21\%, 5.74\%, and 1.38\% respectively, demonstrating good agreement between the SMS and the commercial system.

The diode measurements served as an important baseline validation, as silicon diodes provide more stable and predictable responses compared to perovskite cells. This validation with a well-understood reference substrate establishes confidence in the system's ability to accurately measure more complex photovoltaic substrates.

\subsection{SMS Sample Holder Results}


Figure~\ref{fig:measurementResults}\subref{fig:subfig_measurement1} depicts the distribution of absolute fill-factor (FF) differences between the two platforms, while Table~\ref{tab:agreement-stats} summarises the full set of agreement statistics.  All measurements were performed with a scan range of
0–1.2~V at 50~mV\,s\(^{-1}\).  Each sequence comprised a forward sweep (0\(\rightarrow\)1.2~V) immediately followed by a reverse sweep (1.2\(\rightarrow\)0~V).  The SMS used a fixed step size of 30~mV; the CAS step size varied with its internal auto-resolution mode.

Across 32 substrates (forward and reverse sweeps combined) the mean absolute discrepancies were \(\,0.063\) for FF, \(1.76\; \mathrm{mA\,cm^{-2}}\) for \(J_{\mathrm{SC}}\), and \(0.045\; \mathrm{V}\) for \(V_{\mathrm{OC}}\).  The bias terms in Table~\ref{tab:agreement-stats} reveal a negligible systematic offset in FF (\(-0.004\)), a modest positive bias in \(V_{\mathrm{OC}}\) (\(+30\ \mathrm{mV}\), \(\sim\!2.7\,\%\) of 1.1~V), and a more pronounced bias in \(J_{\mathrm{SC}}\) (\(*****\ \mathrm{mA\,cm^{-2}}\), \(\sim\!5\,\%\) of 24~\(\mathrm{mA\,cm^{-2}}\)). Ninety-five-percent confidence intervals (CIs) exclude zero for the voltage and current metrics, confirming that these biases are statistically significant, whereas the FF bias is not.

The 95~\% limits of agreement (LoA) are considerably wider than the biases, spanning \(\pm0.25\) for FF, \(\pm0.49\ \mathrm{V}\) for \(V_{\mathrm{OC}}\), and \(\pm4\ \mathrm{mA\,cm^{-2}}\) for \(J_{\mathrm{SC}}\).  Such breadth indicates that random, substrate-to-substrate scatter—most likely driven by contact resistance and illumination non-uniformity—dominates the measurement uncertainty.  Whilst acceptable for rapid screening, these LoA values exceed the \(\le\!5\,\%\) relative error often targeted for precision substrate characterisation.

\medskip
\noindent\textbf{MPPT benchmarking.}
One-minute maximum-power-point-tracking (MPPT) runs were initiated at \(85\,\%\) of the \(V_{\mathrm{OC}}\) determined from the preceding JV scan. To avoid transient effects, PCE was calculated from the final 30~s of each trace.  Bias and LoA for PCE mirror the combined behaviour of the individual JV parameters: an average offset of \(+0.95\)\,pp (\(\sim\!5.6\,\%\) relative) and LoA of approximately \(\pm8.5\)\,pp. These values suggest that, while the SMS can follow dynamic power output reliably,
absolute efficiency benchmarking still benefits from subsequent CAS verification.

Overall, the data confirm that the SMS sample holder delivers JV and MPPT measurements that are broadly consistent with the commercial analyser, but hardware refinements—particularly improved probing pressure and optical masking—are required to narrow the LoA to within typical research tolerance bands.

\subsection{SMS with CAS Holder Results}

\subsection{PSC Repeatability Results}

\subsection{Stability Tracking}

The stability measurement Figure~\ref{fig:stability} was taken over INSERT ACTUAL TIME (200 hours). 32 cells were measured in parallel, with the cells showing the most clear degradation shown in the plot. The grow light was used, so PCE values were higher than when measured under the Sinus 70, but this was reasonable for this test, because data on long term stability and degredation behavior under illumination and voltage bias of the cells was desired, rather than the instantaneous PCE output. The average degredation of the well performing cells was (INSERT NUMBER). FIND STATISTIC FOR VARIABILITY AMOUNT, SHOW THAT ALGORITHM IS STABLE.

\subsection{Discussion}
The importance of silver paste in long term measurements is critical. Plots in the supplmemnetary documents show that measurements without silver paste result in high noise in long term measurements. [cite supplmemnetary for graphs].

The SMS costs less than 1\% of traditional stability measurement equipment, while delivering on comparable accuracy and system stability, making it a viable option for academic and research applications. Additionally, in keeping with open-source principles, we have made the hardware schematics, PCB layout, firmware, and software freely available. Development guides are also avaliable in the related GitHub for users to adapt the system to their own use cases and needs. By lowering the financial barriers to wide scale stability testing, this project aims to accelerate the research and development of perovskite solar technology.

Although the error in the direct comparisons of system to system performance was around 10\% between the CAS and SMS, we suspect this is due to moving the substrates between the two setups and the exposure of oxygen during the transfer time. The repeatbility metrics between the two systems

A drawback of the system is that the SMS only implements 2 wire sense, meaning that the same wires are used to supply current and read voltage. It does not implement the 4 wire sensing technique \cite{surmiak2020highthroughput}, which can lead to lower than expected current reading in the SMS. This technique was not implemented as the benefits of 4 wire sense were not crucial to the purposes our lab needed the SMS for. Implementing it would only add unnecessary complication to the board and make the hardware and firmware design more difficult. However, a 4 wire sense setup would be crucial for creating a more accuracy version of the SMS.
Additionally, incorporating previously created low cost environmental chambers, solar simulators, and monitoring systems \cite{keesey2023opensource,zhang2025advancing} with the SMS would provide a affordable, wholistic PSC stability measurement system.

