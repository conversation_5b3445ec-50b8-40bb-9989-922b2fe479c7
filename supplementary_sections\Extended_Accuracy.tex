\section{Extended MPPT Performance}
\label{sec:mppt_extended}

This section provides detailed analysis of the Maximum Power Point Tracking (MPPT) algorithm performance implemented in the Arduino-based system \cite{arduino_nano}. The perturb-and-observe algorithm is commonly used in photovoltaic systems \cite{esram2007comparison} and has been adapted for perovskite solar cell characterization.

% \begin{figure}[ht]
%   \centering
%   \includegraphics[width=0.7\textwidth]{SI_images/mppt_extended.png}
%   \caption{Long‐duration MPPT traces over 24 h under constant illumination.}
%   \label{fig:SI_mppt_long}
% \end{figure}

The MPPT performance is evaluated across different voltage step sizes (0.001–0.01 V) to optimize the trade-off between tracking accuracy and convergence speed. Tables of extracted power conversion efficiency (PCE), convergence time, and oscillation amplitude are provided for each step size configuration. The data analysis utilizes Python libraries including NumPy \cite{numpy} and Pandas \cite{pandas} for statistical evaluation and Matplotlib \cite{matplotlib} for visualization.
