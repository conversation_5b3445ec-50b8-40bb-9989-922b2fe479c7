\section{Assembly Instructions and Guidelines}

This section provides comprehensive assembly instructions for the stability measurement system. All design files, including PCB layouts and 3D models, are available in the open-source repository \cite{stability_setup_supplementary}.

\subsection{Step-by-step PCB assembly guide}
The main PCB can be assembled either through professional services like JLCPCB \cite{jlcpcb_supplementary} or manually by users with surface-mount device (SMD) soldering experience \cite{smd_soldering}. The board includes components such as the MCP4725 DAC \cite{mcp4725_datasheet} and INA219 ADC \cite{ina219_datasheet} that require precise placement and soldering.

\subsection{Instructions for soldering and wiring}
Detailed soldering instructions for connecting the Arduino Nano \cite{arduino_nano} to the main PCB, including proper I$^2$C bus connections \cite{i2c_specification} and power distribution.

\subsection{3D printing guidelines, files, and recommended print settings}
The enclosure and sample holder components should be printed using heat-resistant materials such as ABS \cite{abs_material} or ASA for long-term stability under illumination. While PLA \cite{pla_material} can be used for prototyping, it may warp under extended use. Print files optimized for Bambu Lab printers \cite{bambu_lab_general} are provided, though settings can be adapted for other 3D printers.

\subsection{Enclosure assembly and sample holder setup}
Instructions for assembling the 3D-printed enclosure and configuring the sample holder for optimal solar cell contact and illumination.

\subsection{Troubleshooting common assembly issues}
Common problems encountered during assembly and their solutions, including I$^2$C address conflicts, power supply issues, and mechanical alignment problems.