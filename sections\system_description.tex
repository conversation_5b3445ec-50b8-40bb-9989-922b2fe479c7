\section{System Description}

\begin{figure*}[ht]
    \centering
    \includegraphics[width=1\textwidth]{sections/images/GUI.png}
    \caption{Screenshot of the SMS GUI running in Windows 11, displaying the results of a 32-cell, 5-minute MPPT trial.}
    \label{fig:gui}
\end{figure*}

At the core of each SMS module is a two-layer printed circuit board (PCB) (Fig.~\ref{fig:PhysicalSystem}\subref{fig:system_design_3}) that includes:
\begin{itemize}
  \item An Arduino microcontroller for sequencing and USB communication.
  \item Eight 12-bit Digital-to-Analog Converters (DACs) that supply a programmable bias voltage (0–3.3 V, 0.8 mV steps).
  \item Eight dual-channel Analog-to-Digital Converters (ADCs) for measuring voltage (–0.3–26 V, 4 mV resolution) and current (0–32 mA, 8 µA resolution).
\end{itemize}

Each of the eight channels is fully isolated, allowing for simultaneous JV sweeps or MPPT on all cells. Each channel operates independently with its own dedicated DAC output and ADC input, which prevents electrical crosstalk between measurements. This isolation ensures that the electrical characteristics of one cell do not affect the measurements of adjacent channels, enabling true parallel testing.

The cell holder is a companion PCB that connects to the main board via a 16-pin ribbon cable, with two conductors per channel. Two additional breadboard wires power built-in LEDs for mask alignment. Each cell is contacted by spring-loaded pogo pins within a 3D-printed frame (Fig.~\ref{fig:PhysicalSystem}\subref{fig:system_design_2}), which ensures consistent pressure on both electrodes. With a 600 mm ribbon cable, the resistance of each channel is 2~$\Omega$ between the pogo pin tip and the SMS input/output. The 3D-printed frame also includes a press-fit port for a 2.5~mm internal diameter N$_2$ line. Illumination is provided by a commercially available LED grow light.

Power and data are transmitted over a single USB cable to a host PC. System capacity can be expanded by connecting additional SMS modules via USB. A powered USB hub is recommended when using more than four modules per computer USB port. We have verified stable parallel data collection from eight SMS systems (64 cells) on a Windows 10/11 desktop. A minimum of 12 GB of RAM is recommended, and any modern CPU should be sufficient. The theoretical limit is estimated to be over 32 SMS systems operating in parallel (256 cells).

\subsection{Software}

The SMS is controlled by a Python-based GUI that runs on Windows 10/11. The software allows users to configure and run JV and MPPT measurements, view real-time data, and log results to CSV files. The GUI is designed to be intuitive and user-friendly, and a comprehensive user guide is available in the open-source repository.

Figure~\ref{fig:gui} shows the Python/PyQt GUI. Its key features include:
\begin{itemize}
  \item Interactive configuration of scan parameters (e.g., voltage range, step size, sampling rate).
  \item A test queue for automated saving of test sequences.
  \item Real-time plotting of JV curves and MPPT power traces.
  \item Continuous CSV logging for robust data archiving, which protects against data loss in the event of a PC crash.
\end{itemize}



\subsection{JV Scan}

To characterize initial performance and detect hysteresis, the system conducts a bidirectional voltage sweep (0–3.3 V). At each step, it averages multiple readings to reduce noise. The system then computes the open-circuit voltage (V\textsubscript{OC}) and maximum power point voltage (V\textsubscript{MPP}). The configurable parameters are as follows:

\begin{enumerate}
    \item \textbf{Scan Range:} The voltage span for the scan (0–3.3 V).
    \item \textbf{Scan Step Size:} The voltage increment between readings (0.001–1 V).
    \item \textbf{Cell Area:} The active area of the solar cell (cm$^2$), used to calculate current density.
    \item \textbf{Scan Read Count:} The number of readings averaged per voltage step to reduce noise.
    \item \textbf{Scan Rate:} The speed of the voltage sweep (mV/s).
\end{enumerate}


\begin{figure*}[!htbp]
  \centering
  \setlength{\abovecaptionskip}{5pt}
  \setlength{\belowcaptionskip}{5pt}
  %--- First row of subfigures
  \begin{subfigure}[b]{0.48\textwidth}
    \centering
    \includegraphics[width=\textwidth]{sections/images/Comparison_FF_BoxPlot.png}
    \caption{}
    \label{fig:subfig_measurement1}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \centering
    \includegraphics[width=\textwidth]{sections/images/Comparison_PCE_BoxPlot.png}
    \caption{}
    \label{fig:subfig_measurement2}
  \end{subfigure}

  \vspace{-0.5em}
  %--- Second row of subfigures
  \begin{subfigure}[b]{0.48\textwidth}
    \centering
    \includegraphics[width=\textwidth]{sections/images/JV_Plot.png}
    \caption{}
    \label{fig:subfig_measurement3}
  \end{subfigure}
  \hfill
  \begin{subfigure}[b]{0.48\textwidth}
    \centering
    \includegraphics[width=\textwidth]{sections/images/MPPT_plot.png}
    \caption{}
    \label{fig:subfig_measurement4}
  \end{subfigure}

  \caption{%
    (a)~Box plot of SMS vs CAS Fill Factor on all 4 substrates, with forward (Fw) and reverse (Rv) JV scans separated.
    (b)~Box plot of SMS vs CAS PCE on all 4 substrates
    (c)~JV scan of one substrate from SMS plotted against CAS with substrate in CAS holder. Dotted traces represent SMS while solid traces represent CAS.
    (d)~MPPT plot of one substrate from SMS plotted against CAS  with substrate in CAS holder. Dotted traces represent SMS while solid traces represent CAS.
  }
  \label{fig:measurementResults}
\end{figure*}

\begin{figure*}[!htbp]
  \centering
  \includegraphics[width=1\textwidth]{sections/images/MPPT_plot_long.png}
  \caption{200-hour stability measurement.}
  \label{fig:stability}
\end{figure*}


\subsection{MPPT Algorithm}

We implemented a perturb-and-observe (P\&O) tracking algorithm \cite{esram2007comparison}. The initial voltage can be set manually or automatically by using a prior JV scan to set the start voltage at 85\% of V\textsubscript{OC} \cite{Dai2022}. The configurable parameters are as follows:

\begin{enumerate}
    \item \textbf{Starting Voltage:} The initial load voltage (0–3.3 V) (does not apply if a scan is conducted beforehand).
    \item \textbf{Starting Percentage:}: The percentage of V\textsubscript{OC} to set the starting voltage (only applies if a scan is conducted beforehand).
    \item \textbf{Step Size:} The voltage increment for adjustments (0.001–1 V).
    \item \textbf{Cell Area:} The active area of the solar cell (in cm$^2$) for live PCE calculations.
    \item \textbf{Measurements Per Step:} The number of readings per voltage level that are averaged for noise reduction.
    \item \textbf{Measurement Delay:} The delay between setting the voltage and the measurement, which allows the cell to stabilize (ms).
    \item \textbf{Measurement Interval:} The time interval across which measurements are taken per voltage step (ms).
    \item \textbf{Measurement Time:} The total duration for the MPPT process (specified in minutes or hours).
\end{enumerate}

The P\&O method was selected for its straightforward implementation and low computational demand, which is well-suited for Arduino-based systems. In contrast to methods such as incremental conductance or fuzzy logic control, P\&O does not require extensive calibration or complex algorithms, which improves its reliability. Since our experiments are conducted under stable laboratory conditions, the risk of common P\&O issues, such as oscillation around the MPP or convergence to local minima, is minimal. This makes P\&O a practical and reliable choice for our testing setup \cite{esram2007comparison}.


\subsection{Cost and Assembly}

The majority of the SMS system can be factory-fabricated and assembled, with a minimum order quantity of five per board, for a total cost of under \$275 (excluding shipping and duties) \cite{jlcpcb}. Complete assembly instructions and design files are provided in the open-source repository \cite{StabilitySetup}.
