

\begin{figure*}[ht]
    \centering
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/ENTIRE.png}
        \caption{}
        \label{fig:system_design_1}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/CELL.png}
        \label{fig:system_design_2}
        \caption{}
    \end{subfigure}
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/pcb.png}
        \caption{}
        \label{fig:system_design_3}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.45\textwidth}
        \includegraphics[width=\textwidth]{sections/images/PROBE.png}
        \label{fig:system_design_4}
        \caption{}
    \end{subfigure}
    \caption{Physical overview of the Stability Measurement System (SMS). a) Overall system block diagram. Red arrows indicate data and power connections. b) Detail of the pogo-pin contacts in the 3D-printed enclosure. c) Main PCB assembly: eight independent, electrically isolated measurement channels. d)Sample holder mating with eight cells via spring-loaded pogo pins.}
    \label{fig:PhysicalSystem}
\end{figure*}

\section{Introduction}
Perovskite solar cells (PSCs) have advanced rapidly, with power conversion efficiencies (PCEs) increasing from approximately 4\% in 2009 to over 25.5\% within a decade. They are now rivaling the performance of silicon-based cells while offering simpler and more cost-effective manufacturing processes.\cite{Snaith2013,cheng2021pushing} However, their limited operational stability remains a significant obstacle to commercialization. Environmental factors such as illumination, moisture, oxygen, thermal stress, and electrical bias can induce ion migration, interfacial reactions, and phase segregation, all of which contribute to substrate degradation.\cite{perini2021pressing,zhou2022recent,zhang2022degradation,liu2020uv,dipta2021stability}

To address these stability challenges, specialized instrumentation is required to apply stress conditions and perform electrical measurements. However, commercial stability analyzers are expensive, with costs ranging from \$10,000 to \$60,000.\cite{keesey2023opensource} Lower-cost commercial systems exist, but are often limited to single-cell measurements.\cite{Ossila_IV_System_2025} A notable high-throughput system developed by Köbler et al. offers parallel maximum-power-point tracking across hundreds of perovskite cells; however, its bespoke design and unpublished assembly details make it difficult for other research groups to reproduce.\cite{Kobler2022} These factors restrict the ability of many research groups to conduct comprehensive studies on combinatorial compositional spaces or to obtain statistically significant data from large substrate populations.

In recent years, the open-source hardware movement has provided a practical approach to making high-throughput stability testing more accessible. Community-driven initiatives have produced affordable environmental chambers, LED-based solar simulators, and optical monitoring systems.\cite{keesey2023opensource,zhang2025advancing,Samir2020LED} Although some low-cost, open-source systems for single-channel current-voltage (JV) and maximum power point tracking (MPPT) have been developed, they often require specialized knowledge to reproduce and operate.\cite{Papageorgas2015_IVTracer, csatt2017_IVSwinger2, ElHammoumi2020_RealTimeDAQ} Currently, no open-source instrument is capable of performing automated, parallel MPPT and JV measurements across multiple cells.

This work introduces an easily reproducible, approximately \$100 Arduino-based Stability Measurement System (SMS) designed to fill this gap. Each SMS module includes a custom printed circuit board (PCB), a sample holder, a 3D-printed enclosure, and Python-based control software with a graphical user interface (GUI) to perform parallel JV and MPPT measurements on eight independent pixels. Multiple modules can be connected to a single Windows computer, enabling the monitoring of over 250 cells in parallel. The system requires only basic soldering, 3D printing, and computer skills for assembly.

The SMS provides the core functionality of commercial analyzers at approximately 1\% of the cost. We benchmark the SMS against a commercial system, demonstrate its reliable operation over a 200-hour period, and release all hardware and software under an open-source license to encourage community adoption and further development.\cite{StabilitySetup}

