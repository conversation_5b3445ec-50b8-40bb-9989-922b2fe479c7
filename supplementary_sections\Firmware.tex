\section{Firmware}

The data acquisition firmware, written in C++, runs on each Arduino Nano \cite{arduino_nano}, while a Windows PC sends measurement commands via USB. Each Arduino Nano streams data continuously back to the PC, which is logged in CSV files for later analysis. The firmware utilizes the ATmega328P microcontroller \cite{atmega328p_datasheet} and leverages Adafruit libraries \cite{adafruit_libraries} for I$^2$C communication with the MCP4725 DAC and INA219 ADC components. Two primary measurement modes are implemented:

\subsection{I-V Curve Measurement Mode}
This mode performs current-voltage characterization by sweeping the applied voltage and measuring the resulting current response.

\subsection{Maximum Power Point Tracking (MPPT) Mode}
This mode implements a perturb-and-observe algorithm to continuously track the maximum power point of the solar cell under varying conditions.
