\section{Complete Experimental Data}

This section contains comprehensive experimental datasets collected using the Arduino-based stability measurement system \cite{arduino_nano}. All data processing and analysis were performed using Python libraries including NumPy \cite{numpy}, Pandas \cite{pandas}, and Mat<PERSON>lotlib \cite{matplotlib}. The experimental protocols follow established guidelines for perovskite solar cell stability testing \cite{khenkin2020consensus}.

\subsection{Raw JV curves from diode baseline validation}
Complete current-voltage characterization data for silicon diode validation experiments, demonstrating the accuracy of the INA219 ADC \cite{ina219_datasheet} and MCP4725 DAC \cite{mcp4725_datasheet} measurement chain.

\subsection{Complete dataset for separate holder protocol experiment}
Full experimental dataset comparing measurements taken with different sample holders, including statistical analysis of measurement variations and holder-to-holder reproducibility.

\subsection{Complete dataset for same holder protocol experiment}
Comprehensive data from repeated measurements using the same sample holder, providing insights into measurement repeatability and system stability over time.

\subsection{Raw data and extended analysis for repeatability measurements}
Detailed statistical analysis of measurement repeatability, including standard deviations, confidence intervals, and sources of measurement uncertainty.

\subsection{Extended dataset and degradation curves from 200‑hour stability test}
Complete time-series data from long-duration stability measurements under continuous illumination \cite{led_grow_lights}, including power conversion efficiency trends, maximum power point tracking performance \cite{esram2007comparison}, and degradation rate analysis.
