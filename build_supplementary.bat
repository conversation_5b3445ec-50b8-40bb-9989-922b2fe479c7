@echo off
echo Building Supplementary Information document...
echo.

echo Step 1: Running pdflatex (first pass)...
pdflatex "Supplementary Information.tex"
if %errorlevel% neq 0 (
    echo Error in first pdflatex pass!
    pause
    exit /b 1
)

echo.
echo Step 2: Running bibtex...
bibtex "Supplementary Information"
if %errorlevel% neq 0 (
    echo Error in bibtex!
    pause
    exit /b 1
)

echo.
echo Step 3: Running pdflatex (second pass)...
pdflatex "Supplementary Information.tex"
if %errorlevel% neq 0 (
    echo Error in second pdflatex pass!
    pause
    exit /b 1
)

echo.
echo Step 4: Running pdflatex (final pass)...
pdflatex "Supplementary Information.tex"
if %errorlevel% neq 0 (
    echo Error in final pdflatex pass!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Output: Supplementary Information.pdf
pause
