@article{perini2021pressing,
  author    = {<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>},
  title     = {Pressing challenges in halide perovskite photovoltaics—from the atomic to module level},
  journal   = {Joule},
  volume    = {5},
  pages     = {1024--1030},
  year      = {2021},
  doi       = {10.1016/j.joule.2021.03.011}
}

@article{Kobler2022,
  author  = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, Bern<PERSON> and <PERSON>, Antonio},
  title   = {High‑Throughput Aging System for Parallel Maximum Power Point Tracking of Perovskite Solar Cells},
  journal = {Energy Technology},
  volume  = {10},
  number  = {6},
  pages   = {2200234},
  year    = {2022},
  doi     = {10.1002/ente.202200234},
}

@online{jlcpcb,
  author    = {JLCPCB},
  title     = {JLCPCB: PCB Prototype \& Assembly Manufacturer},
  year      = {2025},
  url       = {https://jlcpcb.com},
  urldate   = {2025-07-09},
  note      = {Founded 2006, Shenzhen (China); quick-turn fabrication, PCB assembly and related services}
}

@article{ali2023outdoor,
  author    = {M. U. Ali and H. Mo and Y. Li and A. B. Djuri{\v{s}}i{\'c}},
  title     = {Outdoor stability testing of perovskite solar cells: Necessary step toward real-life applications},
  journal   = {APL Energy},
  volume    = {1},
  number    = {2},
  pages     = {020903},
  year      = {2023},
  doi       = {10.1063/5.0155845}
}

@article{Dai2022,
  author = {Xuezeng Dai and Shangshang Chen and Yehao Deng and Allen Wood and Guang Yang and Chengbin Fei and Jinsong Huang},
  title = {Pathways to High Efficiency Perovskite Monolithic Solar Modules},
  journal = {PRX Energy},
  volume = {1},
  pages = {013004},
  year = {2022},
  doi = {10.1103/PRXEnergy.1.013004}
}

@article{Kim2024ThermalStability,
  author  = {Sanggyun Kim and Sina Sabury and Carlo A.~R. Perini
             and Tareq Hossain and Augustine O. Yusuf and Xiangyu Xiao
             and Ruipeng Li and Kenneth R. Graham and John R. Reynolds
             and Juan-Pablo Correa-Baena},
  title   = {Enhancing Thermal Stability of Perovskite Solar Cells through
             Thermal Transition and Thin Film Crystallization Engineering
             of Polymeric Hole Transport Layers},
  journal = {ACS Energy Letters},
  year    = {2024},
  volume  = {9},
  pages   = {4501--4508},
  doi     = {10.1021/acsenergylett.4c01546},
  url     = {https://doi.org/10.1021/acsenergylett.4c01546}
}

@misc{StabilitySetup,
  author       = {Chen, Andrew},
  title        = {{Stability-Setup}},
  howpublished = {\url{https://github.com/AndChen153/Stability-Setup}},
  note         = {GitHub repository},
  year         = {2022},
  urldate      = {2025-07-08}
}

@misc{wavelabs_sinus70,
  title = {Sinus 70 Advanced},
  author = {{Wavelabs}},
  howpublished = {\url{https://wavelabs.de/products/sinus-70-advanced/}},
  note = {Accessed: 2025-03-12},
  year = {n.d.}
}

@article{surmiak2020highthroughput,
  title={High‐Throughput Characterization of Perovskite Solar Cells for Rapid Combinatorial Screening},
  author={Surmiak, Maciej Adam and Zhang, Tian and Lu, Jianfeng and Rietwyk, Kevin James and Ruiz Raga, Sonia and McMeekin, David Patrick and Bach, Udo},
  journal={Solar RRL},
  volume={4},
  pages={2000097},
  year={2020},
  doi={10.1002/solr.202000097}
}

@article{zhou2022recent,
  author    = {J. Zhou and others},
  title     = {Recent advances in the combined elevated temperature, humidity, and light stability of perovskite solar cells},
  journal   = {Solar RRL},
  volume    = {6},
  number    = {12},
  pages     = {2200772},
  year      = {2022},
  doi       = {10.1002/solr.202200772}
}

@article{zhang2022degradation,
  author    = {D. Zhang and D. Li and Y. Hu and A. Mei and H. Han},
  title     = {Degradation pathways in perovskite solar cells and how to meet international standards},
  journal   = {Communications Materials},
  volume    = {3},
  pages     = {58},
  year      = {2022},
  doi       = {10.1038/s43246-022-00281-z}
}

@article{liu2020uv,
  author    = {R. Liu and L. Wang and Y. Fan and Z. Li and S. Pang},
  title     = {UV degradation of the interface between perovskites and the electron transport layer},
  journal   = {RSC Advances},
  volume    = {10},
  pages     = {11569--11577},
  year      = {2020},
  doi       = {10.1039/C9RA10960A}
}

@article{dipta2024encapsulating,
  author    = {S. S. Dipta and M. A. Rahim and A. Uddin},
  title     = {Encapsulating perovskite solar cells for long-term stability and prevention of lead toxicity},
  journal   = {Applied Physics Reviews},
  volume    = {11},
  number    = {2},
  pages     = {021301},
  year      = {2024},
  doi       = {10.1063/5.0197154}
}

@article{najafi2022reverse,
  author    = {L. Najafi and others},
  title     = {Reverse-bias and temperature behaviors of perovskite solar cells at extended voltage range},
  journal   = {ACS Applied Energy Materials},
  volume    = {5},
  number    = {2},
  pages     = {1378--1384},
  year      = {2022},
  doi       = {10.1021/acsaem.1c03206}
}

@misc{imec2020scaled,
  author       = {{Solliance/imec}},
  title        = {Scaled perovskite solar modules pass three critical stability tests},
  howpublished = {\url{https://imec-int.com}},
  note         = {Press release, 23 Jan 2020},
  year         = {2020}
}

@article{dipta2021stability,
  author    = {S. S. Dipta and A. Uddin},
  title     = {Stability issues of perovskite solar cells: A critical review},
  journal   = {Energy Technology},
  volume    = {9},
  number    = {11},
  pages     = {2100560},
  year      = {2021},
  doi       = {10.1002/ente.202100560}
}

@article{esram2007comparison,
  title={Comparison of Photovoltaic Array Maximum Power Point Tracking Techniques},
  author={Esram, Trishan and Chapman, Patrick L.},
  journal={IEEE Transactions on Energy Conversion},
  volume={22},
  number={2},
  pages={439--449},
  month={June},
  year={2007},
  doi={10.1109/TEC.2006.874230}
}

@article{Snaith2013,
  author = {Snaith, H. J.},
  title = {Perovskites: The Emergence of a New Era for Low-Cost, High-Efficiency Solar Cells},
  journal = {The Journal of Physical Chemistry Letters},
  volume = {4},
  number = {21},
  pages = {3623-3630},
  year = {2013},
  publisher = {American Chemical Society},
  doi = {10.1021/jz4020162}
}

@article{cheng2021pushing,
  author    = {Y. Cheng and L. Ding},
  title     = {Pushing commercialization of perovskite solar cells by improving their intrinsic stability},
  journal   = {Energy \& Environmental Science},
  volume    = {14},
  number    = {6},
  pages     = {3233--3255},
  year      = {2021},
  doi       = {10.1039/D1EE00493J}
}

@article{xiang2021review,
  author    = {W. Xiang and S. Liu and W. Tress},
  title     = {A review on the stability of inorganic metal halide perovskites: Challenges and opportunities for stable solar cells},
  journal   = {Energy \& Environmental Science},
  volume    = {14},
  number    = {4},
  pages     = {2090--2113},
  year      = {2021},
  doi       = {10.1039/D1EE00157D}
}

@article{duan2022defects,
  author    = {L. Duan and A. Uddin},
  title     = {Defects and stability of perovskite solar cells: A critical analysis},
  journal   = {Materials Chemistry Frontiers},
  volume    = {6},
  pages     = {400--417},
  year      = {2022},
  doi       = {10.1039/D1QM01250A}
}

@article{keesey2023opensource,
  author    = {R. Keesey and others},
  title     = {An open-source environmental chamber for materials-stability testing using an optical proxy},
  journal   = {Digital Discovery},
  volume    = {2},
  pages     = {422--440},
  year      = {2023},
  doi       = {10.1039/D2DD00089J}
}

@online{Ossila_IV_System_2025,
  title     = {Solar Cell I–V Test System},
  year      = {2024},
  url       = {https://www.ossila.com/products/solar-cell-iv-test-system},
  urldate   = {2025-04-28},
  publisher = {Ossila Ltd., Sheffield, UK}
}

@article{Samir2020LED,
  author  = {Samir, Ahmed and Mahgoub, Abdelmomen and Eliwa, Aref and Atia, Doaa M. and El-Madany, Hanaa T. and El-Metwally, Khaled and Zahran, Mohamed},
  title   = {Design and Implementation of LED Solar Simulator},
  journal = {WSEAS Transactions on Power Systems},
  year    = {2020},
  volume  = {15},
  pages   = {68--78},
  doi     = {10.37394/232016.2020.15.8},
  issn    = {1790-5060},
  eissn   = {2224-350X},
  url     = {https://www.wseas.org/multimedia/journals/power/2020/a165116-081.pdf}
}


@article{Papageorgas2015_IVTracer,
  author   = {Papageorgas, Panagiotis and Piromalis, Dimitrios and Valavanis, T. and Kambasis, S. and Iliopoulou, T. and Vokas, G.},
  title    = {A Low-Cost and Fast PV I–V Curve Tracer Based on an Open Source Platform with M2M Communication Capabilities for Preventive Monitoring},
  journal  = {Energy Procedia},
  volume   = {74},
  pages    = {423--438},
  year     = {2015},
  doi      = {10.1016/j.egypro.2015.07.641},
  note     = {Proceedings of the International Conference on Technologies and Materials for Renewable Energy, Environment and Sustainability (TMREES15)},
  publisher= {Elsevier},
  url      = {https://www.sciencedirect.com/science/article/pii/S1876610215014095}
}

@misc{csatt2017_IVSwinger2,
  author       = {{csatt}},
  title        = {IV Swinger 2 – a \$50 I–V Curve Tracer},
  howpublished = {\url{https://www.instructables.com/IV-Swinger-2-a-50-IV-Curve-Tracer/}},
  year         = {2017},
  note         = {Accessed: 2025-04-28}
}

@misc{BambuLabP1S2023,
  author       = {{Bambu Lab}},
  title        = {P1S 3D Printer},
  howpublished = {\url{https://us.store.bambulab.com/collections/3d-printer/products/p1s}},
  year         = {2023},
  note         = {Accessed: 2025-04-28}
}

@misc{ElHammoumi2020_RealTimeDAQ,
  author       = {El Hammoumi, Aboubakr},
  title        = {Real-Time Data Acquisition of Solar Panel Using Arduino and Excel},
  howpublished = {\url{https://projecthub.arduino.cc/Aboubakr_Elhammoumi/real-time-data-acquisition-of-solar-panel-using-arduino-9c72ef}},
  year         = {2020},
  month        = {Mar 25},
  publisher    = {Arduino Project Hub},
  note         = {Accessed: 2025-04-28}
}

@article{khenkin2020consensus,
  author    = {M. V. Khenkin and others},
  title     = {Consensus statement for stability assessment and reporting for perovskite photovoltaics based on ISOS procedures},
  journal   = {Nature Energy},
  volume    = {5},
  number    = {1},
  pages     = {35--49},
  year      = {2020},
  doi       = {10.1038/s41560-019-0529-5}
}

@article{zhang2025advancing,
  author    = {J. Zhang and others},
  title     = {Advancing perovskite photovoltaic technology through machine learning–driven automation},
  journal   = {InfoMat},
  volume    = {7},
  number    = {2},
  pages     = {70005},
  year      = {2025},
  doi       = {10.1002/inf2.70005}
}

@article{hartono2023stability,
  author    = {N. T. P. Hartono and others},
  title     = {Stability follows efficiency based on the analysis of a large perovskite solar cells ageing dataset},
  journal   = {Nature Communications},
  volume    = {14},
  pages     = {4869},
  year      = {2023},
  doi       = {10.1038/s41467-023-40585-3}
}

@article{hartono2024light,
  author    = {N. T. P. Hartono and others},
  title     = {Light cycling as a key to understanding the outdoor behaviour of perovskite solar cells},
  journal   = {Energy \& Environmental Science},
  volume    = {17},
  note      = {Early access},
  year      = {2024},
  doi       = {10.1039/D3EE03508E}
}

@article{kobayashi2021light,
  author    = {E. Kobayashi and others},
  title     = {Light-induced performance increase of carbon-based perovskite solar module for 20-year stability},
  journal   = {Cell Reports Physical Science},
  volume    = {2},
  pages     = {100648},
  year      = {2021},
  doi       = {10.1016/j.xcrp.2021.100648}
}

@article{debastiani2022tandem,
  author    = {M. De Bastiani and M. Babics and E. Aydin and A. S. Subbiah and S. De Wolf},
  title     = {All set for efficient and reliable perovskite/silicon tandem photovoltaic modules?},
  journal   = {Solar RRL},
  volume    = {6},
  number    = {3},
  pages     = {2100493},
  year      = {2022},
  doi       = {10.1002/solr.202100493}
}

@article{repins2024interpreting,
  author    = {I. L. Repins and others},
  title     = {Interpreting accelerated tests on perovskite modules using photooxidation of MAPbI₃ as an example},
  journal   = {Cell Reports Physical Science},
  volume    = {5},
  pages     = {101969},
  year      = {2024},
  doi       = {10.1016/j.xcrp.2024.101969}
}

@article{graniero2023challenge,
  author    = {P. Graniero and others},
  title     = {The challenge of studying perovskite solar cells’ stability with machine learning},
  journal   = {Frontiers in Energy Research},
  volume    = {11},
  pages     = {1118654},
  year      = {2023},
  doi       = {10.3389/fenrg.2023.1118654}
}

% ============================
% Supplementary References
% ============================

% Hardware and Component References
@misc{arduino_nano,
  author       = {{Arduino}},
  title        = {Arduino Nano},
  howpublished = {\url{https://docs.arduino.cc/hardware/nano/}},
  year         = {2024},
  note         = {Accessed: 2025-07-25}
}

@misc{mcp4725_datasheet,
  author       = {{Microchip Technology Inc.}},
  title        = {MCP4725 12-Bit Digital-to-Analog Converter with EEPROM Memory},
  howpublished = {\url{https://ww1.microchip.com/downloads/en/devicedoc/22039d.pdf}},
  year         = {2013},
  note         = {Datasheet}
}

@misc{ina219_datasheet,
  author       = {{Texas Instruments}},
  title        = {INA219 Zero-Drift, Bidirectional Current/Power Monitor with I2C Interface},
  howpublished = {\url{https://www.ti.com/lit/ds/symlink/ina219.pdf}},
  year         = {2015},
  note         = {Datasheet}
}

@misc{easyeda,
  author       = {{EasyEDA}},
  title        = {EasyEDA - Online PCB Design \& Circuit Simulator},
  howpublished = {\url{https://easyeda.com/}},
  year         = {2024},
  note         = {Accessed: 2025-07-25}
}

@misc{atmega328p_datasheet,
  author       = {{Microchip Technology Inc.}},
  title        = {ATmega328P 8-bit AVR Microcontroller},
  howpublished = {\url{https://ww1.microchip.com/downloads/en/DeviceDoc/Atmel-7810-Automotive-Microcontrollers-ATmega328P_Datasheet.pdf}},
  year         = {2015},
  note         = {Datasheet}
}

% Software and Programming References
@misc{pyside6,
  author       = {{The Qt Company}},
  title        = {PySide6 - Qt for Python},
  howpublished = {\url{https://doc.qt.io/qtforpython/}},
  year         = {2024},
  note         = {Accessed: 2025-07-25}
}

@misc{adafruit_libraries,
  author       = {{Adafruit Industries}},
  title        = {Adafruit Arduino Libraries},
  howpublished = {\url{https://github.com/adafruit}},
  year         = {2024},
  note         = {GitHub repositories for Arduino sensor libraries}
}

@misc{python_serial,
  author       = {{Python Software Foundation}},
  title        = {PySerial - Python Serial Port Extension},
  howpublished = {\url{https://pyserial.readthedocs.io/}},
  year         = {2024},
  note         = {Documentation}
}

@misc{matplotlib,
  author       = {{Matplotlib Development Team}},
  title        = {Matplotlib: Python plotting library},
  howpublished = {\url{https://matplotlib.org/}},
  year         = {2024},
  note         = {Accessed: 2025-07-25}
}

@misc{numpy,
  author       = {{NumPy Developers}},
  title        = {NumPy: The fundamental package for scientific computing with Python},
  howpublished = {\url{https://numpy.org/}},
  year         = {2024},
  note         = {Accessed: 2025-07-25}
}

@misc{pandas,
  author       = {{Pandas Development Team}},
  title        = {pandas: Python Data Analysis Library},
  howpublished = {\url{https://pandas.pydata.org/}},
  year         = {2024},
  note         = {Accessed: 2025-07-25}
}

% Manufacturing and Design
@misc{jlcpcb_supplementary,
  author    = {JLCPCB},
  title     = {JLCPCB: PCB Prototype \& Assembly Manufacturer},
  year      = {2025},
  url       = {https://jlcpcb.com},
  urldate   = {2025-07-25},
  note      = {Founded 2006, Shenzhen (China); quick-turn fabrication, PCB assembly and related services}
}

% 3D Printing Materials
@misc{abs_material,
  author       = {{Various Manufacturers}},
  title        = {Acrylonitrile Butadiene Styrene (ABS) 3D Printing Filament},
  note         = {Heat-resistant thermoplastic material for 3D printing}
}

@misc{pla_material,
  author       = {{Various Manufacturers}},
  title        = {Polylactic Acid (PLA) 3D Printing Filament},
  note         = {Standard thermoplastic material for 3D printing}
}

@misc{bambu_lab_general,
  author       = {{Bambu Lab}},
  title        = {Bambu Lab 3D Printers},
  howpublished = {\url{https://bambulab.com/}},
  year         = {2024},
  note         = {Accessed: 2025-07-25}
}

% LED and Illumination
@misc{led_grow_lights,
  author       = {{Various Manufacturers}},
  howpublished = {\url{https://a.co/d/2Sc0gau}},
}

% Standards and Protocols
@misc{i2c_specification,
  author       = {{NXP Semiconductors}},
  title        = {I2C-bus specification and user manual},
  howpublished = {\url{https://www.nxp.com/docs/en/user-guide/UM10204.pdf}},
  year         = {2014},
  note         = {Rev. 6}
}

@misc{open_source_hardware,
  author       = {{Open Source Hardware Association}},
  title        = {Open Source Hardware Definition},
  howpublished = {\url{https://www.oshwa.org/definition/}},
  year         = {2024},
  note         = {Accessed: 2025-07-25}
}

@misc{smd_soldering,
  author       = {{IPC Association}},
  title        = {Surface Mount Device (SMD) Soldering Guidelines},
  note         = {Industry standards for surface mount technology assembly}
}

@misc{component_pricing,
  author       = {{Various Electronics Distributors}},
  title        = {Electronic Component Pricing},
  note         = {Pricing data from Digi-Key, Mouser, and other distributors as of 2025},
  year         = {2025}
}

% GitHub Repository Reference
@misc{stability_setup_supplementary,
  author       = {Chen, Andrew},
  title        = {{Stability Measurement System - Supplementary Files}},
  howpublished = {\url{https://github.com/yourrepo/SMS}},
  note         = {GitHub repository containing Gerber files, STL models, and source code},
  year         = {2025},
  urldate      = {2025-07-25}
}
